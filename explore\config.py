"""
Explore数据分析配置文件
"""

# AWS Athena 连接配置
ATHENA_CONFIG = {
    "aws_access_key_id": "********************",
    "aws_secret_access_key": "fFcxUVjI+xXTqssDK0+6tKMSWXtUQJBeY1ccpFcT",
    "region_name": "us-east-1",
    "s3_output_location": "s3://awsus-datalake-s3log-bucket01/",
    "database": "chervon-awsglobal-bigdata-raw-iot-db01"
}

# Excel文件配置
EXCEL_CONFIG = {
    "source_file": "D:\Filez\DownLoad\DataAnalysis\explore\explore.xlsx",  # 源Excel文件
    "result_file": None  # 如果为None，则自动生成名为 explore_YYYY-MM-DD.xlsx 的文件
}

# SQL查询配置
SQL_QUERIES = {
    # Explore数据统计查询语句
    "explore_statistics": [
        # 序号1 - 曝光次数
        """select count(1) as exposure_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_0_exposure'""",
        
        # 序号2 - 用户数
        """select count(1) as user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_0_exposure' and length(userinfo.email)>0 group by userinfo.email)t""",
        
        # 序号3 - 平均停留时间
        """select avg(cast(expand.residencetime as double)) as avg_duration from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_1_duration' and length(expand.residencetime)>0""",
        
        # 序号4 - 营销卡片点击次数
        """select count(1) as promotion_click_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click')""",
        
        # 序号5 - 营销卡片点击用户数
        """select count(1) as user_cn from (select userinfo.userid from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click') and length(userinfo.userid)>0 group by userinfo.userid)t""",
        
        # 序号6 - 视频播放次数
        """select count(1) as video_play_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_552_0_7_duration')""",
        
        # 序号7 - 视频播放用户数
        """select count(*) as video_play_user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_552_0_7_duration' and length(userinfo.email)>0 group by userinfo.email)t""",
        
        # 序号8 - 图片点击次数
        """select count(1) as photo_click_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_7_click')""",
        
        # 序号9 - 图片点击用户数
        """select count(*) as photo_click_user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_7_click' and length(userinfo.email)>0 group by userinfo.email)t""",
        
        # 序号10 - 视频平均完播率
        """select avg(t1.completion_rate) avg_completion_rate from (select case when t.completion_rate>100 then 100 when t.completion_rate <=1 then t.completion_rate*100 else t.completion_rate end as completion_rate from (select timestamp, platform, cast(REPLACE(expand.completion_rate, ',', '.') as double) as completion_rate from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_552_0_7_duration') and length(expand.completion_rate)>0 )t )t1""",
        
        # 序号11 - 推荐附件曝光次数
        """select count(1) as recommend_exposure_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_0_exposure'""",
        
        # 序号12 - "立即购买"点击次数
        """select count(1) as shop_now_click_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_1_click' and length(expand.model_number)>0""",
        
        # 序号13 - "关闭立即购买"点击次数
        """select count(1) as close_shop_now_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_2_click'"""
    ],
    
    # 营销卡片Model查询语句
    "marketing_card_model": """select case when length(model_number)=0 then '新品集锦' else model_number end as model_number, cn from (select expand.model_number as model_number, count(1) as cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click') group by expand.model_number)t order by cn desc""",
    
    # 推荐附件Model查询语句
    "recommended_parts_model": """select expand.model_number as model_number, count(1) as cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_1_click' and length(expand.model_number)>0 group by expand.model_number order by cn desc""",
       
    # 视频播放量及平均完播率查询语句
    "video_playback_completion_rate": """select video_id, video_cn, concat(cast(avg_rate as varchar), '%') as avg_rate from (select t1.video_id, count(video_id) as video_cn, round(avg(t1.completion_rate), 2) as avg_rate from (select timestamp, platform, video_id, case when completion_rate>100 then 100 when completion_rate <=1 then completion_rate*100 else completion_rate end as completion_rate from (select timestamp, platform, cast(REPLACE(expand.completion_rate, ',', '.') as double) as completion_rate, cast(expand.video_id as bigint)-2000000 as video_id from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_552_0_7_duration') and length(expand.completion_rate)>0 ) t)t1 group by t1.video_id) t2 order by video_id asc""",
    
    # 图片点击量查询语句
    "photo_clicks": """select t.image_id, count(t.image_id) as image_cn from (select timestamp, platform, cast(expand.image_id as bigint)-2000000 as image_id from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_7_click') and length(expand.image_id)>0 ) t group by t.image_id order by t.image_id asc""",
    
    # 用户漏斗查询语句
    "user_funnel": """SELECT user_type, count(*) as user_cn FROM (SELECT user_id, case when cn=1 then 'T1' when cn=2 then 'T2' else 'T3' end as user_type FROM(SELECT user_id, count(*) as cn from (SELECT a.device_id, a.user_id, b.sn, c.commodity_model FROM (SELECT id, device_id, user_id FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY id, device_id ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".app_user_device a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U')) a left join (SELECT id, product_id, device_id, sn FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".device a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U')) b on a.device_id = b.device_id left join (SELECT id, model, commodity_model, product_sn_code, category_id FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY id, commodity_model ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".product a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U') and category_id<>1666279457297682434) c on b.product_id=c.id WHERE b.sn is not null and c.commodity_model is not null)t group by user_id )t1 )t2 group by user_type"""
}

