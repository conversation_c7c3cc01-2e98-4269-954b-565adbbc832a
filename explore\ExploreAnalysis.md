我有个简单的数据分析需求，分析结果写入excel文件。
需求分析过程如下：
1、通过创建Athena客户端，执行sql查询结果数据，然后将结果数据写入excel内。其中创建连接Athena的aksk、region、s3 location等参数是可配置的固定值

2、具体分析需求在 explore.xlsx 中，其中【Explore数据统计】sheet页 “序号” 列是具体的分析需求编号，且每个编号对应需求的统计sql如下：
  1 -> select count(1) as exposure_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_0_exposure'

  2 -> select count(1) as user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_0_exposure' and length(userinfo.email)>0 group by userinfo.email)t

  3 -> select avg(cast(expand.residencetime as double)) as avg_duration from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_1_duration' and length(expand.residencetime)>0

  4 -> select count(1) as promotion_click_cn  from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click')

  5 -> select count(1) as user_cn from (select userinfo.userid  from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click') and length(userinfo.userid)>0 group by userinfo.userid)t

  6 -> select count(1) as video_play_cn  from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_6_click')

  7 -> select count(*) as video_play_user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_6_click' and length(userinfo.email)>0 group by userinfo.email)t

  8 -> select count(1) as photo_click_cn  from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_7_click')

  9 -> select count(*) as photo_click_user_cn from (select userinfo.email from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid ='106_551_0_7_click' and length(userinfo.email)>0 group by userinfo.email)t

  10 -> select avg(t1.completion_rate) avg_completion_rate from (select case when t.completion_rate>100 then 100 when t.completion_rate <=1 then t.completion_rate*100 else t.completion_rate end as completion_rate from (select timestamp, platform, cast(REPLACE(expand.completion_rate, ',', '.') as double) as completion_rate from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_552_0_7_duration') and length(expand.completion_rate)>0 )t )t1

  11 -> select count(1) as recommend_exposure_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_0_exposure'

  12 -> select count(1) as shop_now_click_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_1_click' and length(expand.model_number)>0
  
  13 -> select count(1) as close_shop_now_cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_2_click'

数据分析结果写入explore.xlsx中的该序号行的最后一列，且列名为执行代码当天的日期。
另，【Explore数据统计】sheet页，序号14需求的值由：序号5的值/序号2的值 计算得到；序号15需求的值由：序号12的值/序号11的值 计算得到。

3、【营销卡片（点击）Model】sheet页由如下sql计算得到，且计算结果写入执行代码当天的日期列。
且每次查询结果在数据写入的时候，需将对应model_number的值写入日期列，如果当天查询得到的model_number与之前查到过的model_number比较为新增的model_number时，则新加一行，Model列填新的model_number，日期列填当天查到的cn值，之前其余天的改行填为0。
select case when length(model_number)=0 then '新品集锦' else model_number end as model_number, cn from (select expand.model_number as model_number, count(1) as cn  from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_2_click','106_551_0_3_click','106_551_0_4_click') group by expand.model_number)t order by cn desc

4、【推荐附件（点击）Model】sheet页结果由如下sql计算得到，且计算结果写入执行代码当天的日期列。
且每次查询结果在数据写入的时候，需将对应model_number的值写入日期列，如果当天查询得到的model_number与之前查到过的model_number比较为新增的model_number时，则新加一行，Model列填新的model_number，日期列填当天查到的cn值，之前其余天的改行填为0。
select expand.model_number as model_number, count(1) as cn from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='12' and eventid ='12_550_0_1_click' and length(expand.model_number)>0 group by expand.model_number order by cn desc

5、【视频播放量及平均完播率】sheet页由如下sql计算得到，且计算结果写入执行代码当天的日期列（播放量、完播率）。
select video_id, video_cn, concat(cast(avg_rate as varchar), '%') as avg_rate  from (select t1.video_id, count(video_id) as video_cn, round(avg(t1.completion_rate), 2) as avg_rate from (select timestamp, platform, video_id, case when completion_rate>100 then 100 when completion_rate <=1 then completion_rate*100 else completion_rate end as completion_rate from (select timestamp, platform, cast(REPLACE(expand.completion_rate, ',', '.') as double) as completion_rate, cast(expand.video_id as bigint)-2000000 as video_id from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_552_0_7_duration') and length(expand.completion_rate)>0 ) t)t1 group by t1.video_id) t2 order by video_id asc

6、【图片点击量】sheet由如下sql计算得到，且计算结果写入执行代码当天的日期列。
select t.image_id, count(t.image_id) as image_cn from (select timestamp, platform, cast(expand.image_id as bigint)-2000000 as image_id from "chervon-awsglobal-bigdata-raw-iot-db01".iot_tracking_na where year >= '2025' and moduleid='106' and eventid in ('106_551_0_7_click') and length(expand.image_id)>0 ) t group by t.image_id order by t.image_id asc

7、【用户漏斗】sheet由如下sql计算得到，且计算结果写入执行代码当天的日期列。
SELECT user_type, count(*) as user_cn FROM (SELECT user_id, case when cn=1 then 'T1' when cn=2 then 'T2' else 'T3' end as user_type FROM(SELECT user_id, count(*) as cn from (SELECT a.device_id, a.user_id, b.sn, c.commodity_model FROM (SELECT id, device_id, user_id FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY id, device_id ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".app_user_device a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U')) a left join (SELECT id, product_id, device_id, sn FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".device a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U')) b on a.device_id = b.device_id left join (SELECT id, model, commodity_model, product_sn_code, category_id FROM (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY id, commodity_model ORDER BY update_time desc) as row_num FROM "chervon-awsglobal-bigdata-raw-iot-db01".product a) t WHERE row_num = 1 and is_deleted=0 and op in ('I', 'U') and category_id<>1666279457297682434) c on b.product_id=c.id WHERE b.sn is not null and c.commodity_model is not null)t group by user_id )t1 )t2 group by user_type


8、数据写入excel之后之前的历史的数据均需要保留，且新数据记录的列头格式需要与历史数据的列头格式保持完全一致。

9、代码编写完成之后，请帮忙编写一个测试写excel文件的代码，结果可以用demo数据进行代替。