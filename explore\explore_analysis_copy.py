# -*- coding: utf-8 -*-

"""
Explore数据分析程序

该程序通过AWS Athena连接数据库，查询分析数据并将结果写入Excel文件。
"""

import os
import boto3
import time
import pandas as pd
from datetime import datetime
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
import shutil

# 导入配置
from config_copy import ATHENA_CONFIG, EXCEL_CONFIG, SQL_QUERIES


class ExploreAnalysis:
    """Explore数据分析类"""

    def find_latest_result_file(self, source_excel_path):
        """
        查找最近的结果文件，按日期降序排序，返回最新的文件路径
        
        Args:
            source_excel_path: 源Excel文件路径，如果找不到结果文件将使用此文件
            
        Returns:
            最新的结果文件路径
        """
        # 获取当前目录下所有explore_开头的Excel文件
        explore_files = []
        current_dir = os.path.dirname(os.path.abspath(source_excel_path))
        
        for file in os.listdir(current_dir):
            if file.startswith("explore_") and file.endswith(".xlsx"):
                file_path = os.path.join(current_dir, file)
                
                # 尝试从文件名提取日期
                try:
                    # 文件名格式应为explore_YYYY-MM-DD.xlsx
                    date_str = file.replace("explore_", "").replace(".xlsx", "")
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")
                    explore_files.append((file_path, file_date))
                except:
                    # 如果无法解析日期，跳过此文件
                    continue
        
        # 按日期降序排序
        explore_files.sort(key=lambda x: x[1], reverse=True)
        
        # 返回最新的文件，如果没有找到，则返回源文件
        if explore_files:
            latest_file = explore_files[0][0]
            print(f"找到最近的结果文件: {latest_file}")
            return latest_file
        else:
            print(f"没有找到历史结果文件，将使用源文件: {source_excel_path}")
            return source_excel_path
    
    def __init__(self, source_excel_path=None, result_excel_path=None):
        """
        初始化分析类
        
        Args:
            source_excel_path: 源Excel文件路径，如果为None则使用配置中的值
            result_excel_path: 结果Excel文件路径，如果为None则自动生成
        """
        # 设置源Excel文件路径
        if source_excel_path is None:
            source_excel_path = EXCEL_CONFIG["source_file"]
        self.source_excel_path = source_excel_path
        
        # 当前日期
        self.today = datetime.now().strftime('%Y-%m-%d')
        
        # 设置结果Excel文件路径
        if result_excel_path is None:
            if EXCEL_CONFIG["result_file"] is None:
                result_excel_path = f"explore_{self.today}.xlsx"
            else:
                result_excel_path = EXCEL_CONFIG["result_file"]
        self.result_excel_path = result_excel_path
        
        # 查找最近的结果文件作为基础文件
        base_file = self.find_latest_result_file(source_excel_path)
        
        # 检查基础文件是否存在
        if not os.path.exists(base_file):
            raise FileNotFoundError(f"Excel文件 {base_file} 不存在")
        
        # 如果基础文件与目标结果文件路径不同，则复制
        if base_file != self.result_excel_path:
            shutil.copy2(base_file, self.result_excel_path)
            print(f"已复制 {base_file} 到 {self.result_excel_path}")
        else:
            print(f"使用现有文件 {base_file}")
        
        # 加载工作簿
        self.workbook = load_workbook(self.result_excel_path)
        
        # 初始化AWS Athena客户端
        try:
            self.athena_client = boto3.client(
                'athena',
                aws_access_key_id=ATHENA_CONFIG["aws_access_key_id"],
                aws_secret_access_key=ATHENA_CONFIG["aws_secret_access_key"],
                region_name=ATHENA_CONFIG["region_name"]
            )
            print("AWS Athena连接成功")
            
            # 设置S3输出位置
            self.s3_output_location = ATHENA_CONFIG["s3_output_location"]
            self.database = ATHENA_CONFIG["database"]
        except Exception as e:
            raise ConnectionError(f"连接AWS Athena失败: {e}")
        
    def copy_cell_format(self, source_cell, target_cell):
        """
        复制单元格格式
        
        Args:
            source_cell: 源单元格
            target_cell: 目标单元格
        """
        # 简单复制字体属性
        target_cell.font = Font(
            name=source_cell.font.name,
            size=source_cell.font.size,
            bold=source_cell.font.bold,
            italic=source_cell.font.italic,
            underline=source_cell.font.underline,
            color=source_cell.font.color
        )
        
        # 复制对齐方式
        target_cell.alignment = Alignment(
            horizontal=source_cell.alignment.horizontal,
            vertical=source_cell.alignment.vertical,
            textRotation=source_cell.alignment.textRotation,
            wrapText=source_cell.alignment.wrapText
        )
        
        # 复制填充
        if source_cell.fill.fill_type:
            target_cell.fill = PatternFill(
                fill_type=source_cell.fill.fill_type,
                start_color=source_cell.fill.start_color,
                end_color=source_cell.fill.end_color
            )
        
        # 复制边框 (简化处理)
        if source_cell.border:
            target_cell.border = Border(
                left=source_cell.border.left,
                right=source_cell.border.right,
                top=source_cell.border.top,
                bottom=source_cell.border.bottom
            )
    
    def get_column_format_template(self, sheet):
        """
        获取列头格式模板单元格
        
        Args:
            sheet: 工作表对象
            
        Returns:
            模板单元格对象
        """
        template_cell = None
        
        # 查找第一个非空列头作为模板
        for col in range(2, sheet.max_column + 1):
            header_cell = sheet.cell(row=1, column=col)
            if header_cell.value:
                template_cell = header_cell
                break
        
        return template_cell
    
    def apply_column_format(self, cell, template_cell):
        """
        应用列头格式模板
        
        Args:
            cell: 要应用格式的单元格对象
            template_cell: 模板单元格对象
        """
        if not template_cell:
            return
        
        self.copy_cell_format(template_cell, cell)

    def get_date_format_from_cell(self, cell_value):
        """
        从单元格值获取日期格式
        
        Args:
            cell_value: 单元格值
            
        Returns:
            日期格式字符串, 实际显示格式
        """
        if not cell_value:
            return None, None
            
        # 将日期转换为字符串以检查格式
        if isinstance(cell_value, datetime):
            cell_str = cell_value.strftime("%Y/%m/%d")
        else:
            cell_str = str(cell_value)
            
        # 检查日期分隔符和格式
        if "/" in cell_str and len(cell_str.split("/")) == 3:
            parts = cell_str.split("/")
            # 检查月和日是否有前导零
            if len(parts[1]) == 1 and len(parts[2]) == 1:  # 2025/4/27格式
                return "%Y/%-m/%-d" if os.name != 'nt' else "%Y/%#m/%#d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            elif len(parts[1]) == 1:  # 2025/4/07格式
                return "%Y/%-m/%d" if os.name != 'nt' else "%Y/%#m/%d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            elif len(parts[2]) == 1:  # 2025/04/7格式
                return "%Y/%m/%-d" if os.name != 'nt' else "%Y/%m/%#d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            else:  # 2025/04/07格式
                return "%Y/%m/%d", f"{parts[0]}/{parts[1]}/{parts[2]}"
        elif "-" in cell_str and len(cell_str.split("-")) == 3:
            return "%Y-%m-%d", cell_str
        elif "年" in cell_str and "月" in cell_str and "日" in cell_str:
            return "%Y年%m月%d日", cell_str
        
        return None, None

    def find_or_create_date_column(self, sheet):
        """
        查找或创建日期列，确保格式一致
        
        Args:
            sheet: 工作表对象
            
        Returns:
            日期列的索引
        """
        # 获取列头格式模板单元格
        format_template_cell = self.get_column_format_template(sheet)
        
        # 查找日期列
        date_col = None
        date_format = None
        display_format = None
        
        # 查找现有日期列的格式
        for col in range(2, sheet.max_column + 1):
            header = sheet.cell(row=1, column=col).value
            if header:
                # 尝试识别日期格式
                fmt, disp = self.get_date_format_from_cell(header)
                if fmt:
                    date_format = fmt
                    display_format = disp
                    break
        
        # 如果找不到日期格式，使用默认格式 (2025/4/14，没有前导零)
        if not date_format:
            date_format = "%Y/%#m/%#d" if os.name == 'nt' else "%Y/%-m/%-d"
            now = datetime.now()
            display_format = f"{now.year}/{now.month}/{now.day}"
        
        # 使用识别出的格式生成今天的日期
        now = datetime.now()
        if display_format:
            # 直接按照显示格式构建字符串
            today_formatted = f"{now.year}/{now.month}/{now.day}"
        else:
            # 使用strftime格式化（不推荐，因为Windows不支持某些格式符）
            try:
                today_formatted = now.strftime(date_format)
            except:
                today_formatted = f"{now.year}/{now.month}/{now.day}"
                
        print(f"识别到的日期格式: {date_format}, 显示格式: {display_format}")
        print(f"格式化后的今天日期: {today_formatted}")
        
        # 查找最后一个非空列和第一个空列
        last_non_empty_col = 1
        next_empty_col = None
        
        # 从右向左找最后一个非空列
        for col in range(sheet.max_column, 0, -1):
            col_has_data = False
            for row in range(1, min(10, sheet.max_row + 1)):  # 检查前几行
                if sheet.cell(row=row, column=col).value is not None:
                    col_has_data = True
                    last_non_empty_col = col
                    break
            if col_has_data:
                break
                
        # 下一个可用列就是最后非空列的后一列
        next_empty_col = last_non_empty_col + 1
        
        # 查找是否已存在今天的日期列
        date_col = None
        for col in range(1, last_non_empty_col + 1):
            header = sheet.cell(row=1, column=col).value
            if header:
                header_str = str(header)
                today_str = today_formatted
                
                # 尝试将两者解析为日期对象来比较
                try:
                    header_date = datetime.strptime(header_str, date_format)
                    today_date = datetime.strptime(today_str, date_format)
                    if header_date.date() == today_date.date():
                        date_col = col
                        print(f"找到现有日期列 {date_col}: {header_str}")
                        break
                except:
                    # 直接比较字符串
                    if header_str == today_str:
                        date_col = col
                        print(f"找到现有日期列 {date_col}: {header_str}")
                        break
        
        # 如果没有找到今天的日期列，则创建新列
        if date_col is None:
            date_col = next_empty_col
            header_cell = sheet.cell(row=1, column=date_col)
            header_cell.value = today_formatted
            self.apply_column_format(header_cell, format_template_cell)
            print(f"在列 {date_col} 创建新的日期列: {today_formatted}")
        
        return date_col
    
    def execute_query(self, sql):
        """
        执行AWS Athena SQL查询
        
        Args:
            sql: SQL查询语句
            
        Returns:
            查询结果，格式与TDengine兼容: {'data': [[value1], [value2], ...]}
        """
        try:
            print(f"执行AWS Athena查询: {sql[:80]}...")  # 只打印前80个字符，避免日志过长
            
            # 启动查询
            response = self.athena_client.start_query_execution(
                QueryString=sql,
                QueryExecutionContext={
                    'Database': self.database
                },
                ResultConfiguration={
                    'OutputLocation': self.s3_output_location,
                }
            )
            
            # 获取查询执行ID
            query_execution_id = response['QueryExecutionId']
            print(f"查询ID: {query_execution_id}")
            
            # 等待查询完成
            state = 'RUNNING'
            max_attempts = 150  # 设置最大尝试次数，避免无限循环
            attempts = 0
            
            while state in ['RUNNING', 'QUEUED'] and attempts < max_attempts:
                response = self.athena_client.get_query_execution(
                    QueryExecutionId=query_execution_id
                )
                state = response['QueryExecution']['Status']['State']
                
                if state in ['RUNNING', 'QUEUED']:
                    print(f"查询状态: {state}，等待...")
                    time.sleep(1)  # 等待1秒后再检查
                    attempts += 1
            
            # 检查查询状态
            if state == 'SUCCEEDED':
                # 获取查询结果
                response = self.athena_client.get_query_results(
                    QueryExecutionId=query_execution_id
                )
                
                # 解析结果，转换为与TDengine兼容的格式
                rows = []
                for row in response['ResultSet']['Rows'][1:]:  # 跳过标题行
                    # 提取每行的实际值
                    values = [col.get('VarCharValue', None) for col in row['Data']]
                    rows.append(values)
                
                print(f"查询成功，返回 {len(rows)} 条结果")
                return {"data": rows}
            else:
                print(f"查询失败，状态: {state}")
                if 'StateChangeReason' in response['QueryExecution']['Status']:
                    print(f"原因: {response['QueryExecution']['Status']['StateChangeReason']}")
                return {"data": []}
                
        except Exception as e:
            print(f"查询执行失败: {e}")
            return {"data": []}
    
    def get_cell_template(self, sheet, start_row, end_row, start_col=2):
        """
        获取单元格格式模板单元格
        
        Args:
            sheet: 工作表对象
            start_row: 起始行
            end_row: 结束行
            start_col: 起始列
            
        Returns:
            模板单元格对象
        """
        template_cell = None
        # 查找第一个非空数据单元格作为模板
        for col in range(start_col, sheet.max_column + 1):
            for row in range(start_row, end_row + 1):
                data_cell = sheet.cell(row=row, column=col)
                if data_cell.value is not None:
                    template_cell = data_cell
                    break
            if template_cell:
                break
        return template_cell
            
    def process_explore_statistics(self):
        """处理【Explore数据统计】sheet页数据"""
        print("\n开始处理【Explore数据统计】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["Explore数据统计"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=13)
        
        # 执行11个SQL查询
        for i, sql in enumerate(SQL_QUERIES["explore_statistics"]):
            try:
                result = self.execute_query(sql)
                
                if result and len(result['data']) > 0:
                    value = result['data'][0][0]
                    cell = sheet.cell(row=i+2, column=date_col)
                    
                    # 序号3（Explore页面平均停留时长）的值需要四舍五入保留0位小数
                    if i == 2:  # 索引从0开始，对应序号3
                        try:
                            # 安全转换：确保value是数字类型
                            if isinstance(value, str):
                                # 移除可能的非数字字符
                                value = value.replace(',', '').strip()
                                if not value:  # 如果是空字符串，设为0
                                    value = 0
                            value = float(value)  # 转换为浮点数
                            value = round(value, 0)  # 四舍五入保留0位小数
                            value = int(value)  # 确保显示为整数
                        except (ValueError, TypeError) as e:
                            print(f"处理序号3时类型转换出错: {e}，原值: {value}，使用默认值0")
                            value = 0
                        
                    # 序号8（所有视频平均完播率）的值需要存为xx%格式
                    elif i == 7:  # 索引从0开始，对应序号8
                        try:
                            if isinstance(value, str):
                                # 移除可能的百分号和其他非数字字符
                                value = value.replace('%', '').replace(',', '').strip()
                                if not value:  # 如果是空字符串，设为0
                                    value = 0
                            value = float(value)  # 转换为浮点数
                            value = f"{value:.2f}%"  # 保留2位小数并添加百分号
                        except (ValueError, TypeError) as e:
                            print(f"处理序号8时类型转换出错: {e}，原值: {value}，使用默认值0%")
                            value = "0.00%"
                        
                    cell.value = value
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"序号 {i+1} 查询结果: {value}")
                else:
                    cell = sheet.cell(row=i+2, column=date_col)
                    cell.value = 0
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"序号 {i+1} 查询无结果，填充0")
            except Exception as e:
                print(f"处理序号 {i+1} 时出错: {e}")
                cell = sheet.cell(row=i+2, column=date_col)
                cell.value = "ERROR"
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                
        # 处理序号12和13的公式计算
        # 序号12: 序号5的值 / 序号2的值 * 100，保留2位小数
        cell_12 = sheet.cell(row=13, column=date_col)
        cell_5 = sheet.cell(row=6, column=date_col).value  # 序号5对应第6行
        cell_2 = sheet.cell(row=3, column=date_col).value  # 序号2对应第3行
        
        # 确保进行类型转换
        try:
            # 将单元格值转换为浮点数
            val_5 = float(cell_5) if cell_5 and cell_5 != "ERROR" else 0
            val_2 = float(cell_2) if cell_2 and cell_2 != "ERROR" else 0
            
            if val_2 > 0:
                result = (val_5 / val_2) * 100
                cell_12.value = round(result, 2)  # 保留2位小数
                if cell_template:
                    self.apply_column_format(cell_12, cell_template)
                print(f"序号 12 计算结果: {cell_12.value}")
            else:
                cell_12.value = 0
                if cell_template:
                    self.apply_column_format(cell_12, cell_template)
                print("序号 12 计算失败（除数为0），填充0")
        except Exception as e:
            print(f"计算序号 12 时出错: {e}")
            cell_12.value = 0
            if cell_template:
                self.apply_column_format(cell_12, cell_template)
            
        # 序号13: 序号10的值 / 序号9的值 * 100，保留2位小数
        cell_13 = sheet.cell(row=14, column=date_col)
        cell_10 = sheet.cell(row=11, column=date_col).value  # 序号10对应第11行
        cell_9 = sheet.cell(row=10, column=date_col).value   # 序号9对应第10行
        
        # 确保进行类型转换
        try:
            # 将单元格值转换为浮点数
            val_10 = float(cell_10) if cell_10 and cell_10 != "ERROR" else 0
            val_9 = float(cell_9) if cell_9 and cell_9 != "ERROR" else 0
            
            if val_9 > 0:
                result = (val_10 / val_9) * 100
                cell_13.value = round(result, 2)  # 保留2位小数
                if cell_template:
                    self.apply_column_format(cell_13, cell_template)
                print(f"序号 13 计算结果: {cell_13.value}")
            else:
                cell_13.value = 0
                if cell_template:
                    self.apply_column_format(cell_13, cell_template)
                print("序号 13 计算失败（除数为0），填充0")
        except Exception as e:
            print(f"计算序号 13 时出错: {e}")
            cell_13.value = 0
            if cell_template:
                self.apply_column_format(cell_13, cell_template)
            
    def process_marketing_card_model(self):
        """处理【营销卡片（点击）Model】sheet页数据"""
        print("\n开始处理【营销卡片（点击）Model】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["营销卡片（点击）Model"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["marketing_card_model"])
        
        if not result or len(result['data']) == 0:
            print("营销卡片查询无结果")
            return
                
        # 获取现有model_number列表
        existing_models = {}
        max_existing_row = 2  # 跟踪最大的已存在行号
        for row in range(2, sheet.max_row + 1):
            model = sheet.cell(row=row, column=1).value
            if model:
                existing_models[model] = row
                max_existing_row = max(max_existing_row, row)
                
        # 初始化所有现有model的当天数据为0
        for row in range(2, max_existing_row + 1):
            # 确保每一行都有数据，即使这行没有Model名称
            cell = sheet.cell(row=row, column=date_col)
            cell.value = 0
            if cell_template:
                self.apply_column_format(cell, cell_template)
                
        # 处理查询结果
        # 找到第一个空行，确保数据紧接着写入
        current_row = 2  # 从第二行开始查找（第一行是标题）
        while current_row <= sheet.max_row and sheet.cell(row=current_row, column=1).value is not None:
            current_row += 1
        for model_data in result['data']:
            model_number = model_data[0]
            cn_value = model_data[1]
            
            if model_number in existing_models:
                # 更新现有model
                row = existing_models[model_number]
                cell = sheet.cell(row=row, column=date_col)
                cell.value = cn_value
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                print(f"更新营销卡片Model: {model_number}, 值: {cn_value}")
            else:
                # 添加新model
                model_cell = sheet.cell(row=current_row, column=1)
                model_cell.value = model_number
                
                # 给Model列添加实线边框
                model_cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # 为新model在所有历史日期列中填充0
                for col in range(2, sheet.max_column + 1):
                    if col != date_col and sheet.cell(row=1, column=col).value is not None:
                        # 检查该列是否是日期列
                        header = sheet.cell(row=1, column=col).value
                        if isinstance(header, datetime) or isinstance(header, str):
                            # 填充0值
                            hist_cell = sheet.cell(row=current_row, column=col)
                            hist_cell.value = 0
                            if cell_template:
                                self.apply_column_format(hist_cell, cell_template)
                
                # 在当前日期列填写实际值
                cell = sheet.cell(row=current_row, column=date_col)
                cell.value = cn_value
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                    
                print(f"添加新营销卡片Model: {model_number}, 值: {cn_value}")
                current_row += 1
            
    def process_recommended_parts_model(self):
        """处理【推荐附件（点击）Model】sheet页数据"""
        print("\n开始处理【推荐附件（点击）Model】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["推荐附件（点击）Model"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["recommended_parts_model"])
        
        if not result or len(result['data']) == 0:
            print("推荐附件查询无结果")
            return
                
        # 获取现有model_number列表
        existing_models = {}
        max_existing_row = 2  # 跟踪最大的已存在行号
        for row in range(2, sheet.max_row + 1):
            model = sheet.cell(row=row, column=1).value
            if model:
                existing_models[model] = row
                max_existing_row = max(max_existing_row, row)
                
        # 初始化所有现有model的当天数据为0
        for row in range(2, max_existing_row + 1):
            # 确保每一行都有数据，即使这行没有Model名称
            cell = sheet.cell(row=row, column=date_col)
            cell.value = 0
            if cell_template:
                self.apply_column_format(cell, cell_template)
                
        # 处理查询结果
        # 找到第一个空行，确保数据紧接着写入
        current_row = 2  # 从第二行开始查找（第一行是标题）
        while current_row <= sheet.max_row and sheet.cell(row=current_row, column=1).value is not None:
            current_row += 1
        for model_data in result['data']:
            model_number = model_data[0]
            cn_value = model_data[1]
            
            if model_number in existing_models:
                # 更新现有model
                row = existing_models[model_number]
                cell = sheet.cell(row=row, column=date_col)
                cell.value = cn_value
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                print(f"更新推荐附件Model: {model_number}, 值: {cn_value}")
            else:
                # 添加新model
                model_cell = sheet.cell(row=current_row, column=1)
                model_cell.value = model_number
                
                # 给Model列添加实线边框
                model_cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                
                # 为新model在所有历史日期列中填充0
                for col in range(2, sheet.max_column + 1):
                    if col != date_col and sheet.cell(row=1, column=col).value is not None:
                        # 检查该列是否是日期列
                        header = sheet.cell(row=1, column=col).value
                        if isinstance(header, datetime) or isinstance(header, str):
                            # 填充0值
                            hist_cell = sheet.cell(row=current_row, column=col)
                            hist_cell.value = 0
                            if cell_template:
                                self.apply_column_format(hist_cell, cell_template)
                
                # 在当前日期列填写实际值
                cell = sheet.cell(row=current_row, column=date_col)
                cell.value = cn_value
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                
                print(f"添加新推荐附件Model: {model_number}, 值: {cn_value}")
                current_row += 1
            
    def process_video_completion_rate(self):
        """处理【平均完播率最低|高的视频ID】sheet页数据"""
        print("\n开始处理【平均完播率最低|高的视频ID】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["平均完播率最低|高的视频ID"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取格式模板 - 从现有单元格复制格式
        # 寻找ID和完播率的示例单元格
        id_template = None
        rate_template = None
        
        # 查找现有的ID和完播率单元格作为模板
        for col in range(2, sheet.max_column):
            if sheet.cell(row=2, column=col).value == "ID" and sheet.cell(row=2, column=col+1).value == "完播率":
                id_template = sheet.cell(row=3, column=col)  # 使用第3行(索引2)的完播率最低行作为模板
                rate_template = sheet.cell(row=3, column=col+1)
                break
        
        # 如果找不到现有模板，使用通用模板
        if not id_template or not rate_template:
            template_cell = self.get_cell_template(sheet, start_row=2, end_row=4)
            id_template = template_cell
            rate_template = template_cell
            
        # 创建日期标题跨列显示
        id_col = date_col
        rate_col = date_col + 1
        
        # 获取日期列表格式模板
        date_template = self.get_column_format_template(sheet)
        
        # 设置日期标题在第一列
        date_header = sheet.cell(row=1, column=id_col)
        # 获取当前日期的格式化字符串，保持与find_or_create_date_column一致
        now = datetime.now()
        today_formatted = f"{now.year}/{now.month}/{now.day}"
        
        date_header.value = today_formatted
        if date_template:
            self.apply_column_format(date_header, date_template)
        
        # 清空第二列的日期标题（保持与Excel模板一致的格式）
        date_header2 = sheet.cell(row=1, column=rate_col)
        date_header2.value = None
        
        # 合并日期单元格，使其横跨两列
        try:
            # 通过合并字符串来指定单元格范围
            merge_range = f"{get_column_letter(id_col)}1:{get_column_letter(rate_col)}1"
            sheet.merge_cells(merge_range)
            print(f"合并日期单元格: {merge_range}")
        except Exception as e:
            print(f"合并单元格时出错: {e}")
            
        # 设置ID和完播率作为子标题（第2行）
        id_header = sheet.cell(row=2, column=id_col)
        id_header.value = "ID"
        if date_template:
            self.apply_column_format(id_header, date_template)
            
        rate_header = sheet.cell(row=2, column=rate_col)
        rate_header.value = "完播率"
        if date_template:
            self.apply_column_format(rate_header, date_template)
            
        # 执行SQL查询 - 完播率最高的视频
        highest_result = self.execute_query(SQL_QUERIES["highest_completion_rate"])
        if highest_result and len(highest_result['data']) > 0:
            video_id = highest_result['data'][0][0]
            rate = highest_result['data'][0][1]
            
            # 填写"完播率最高"行 (第3行，索引2)的数据
            id_cell = sheet.cell(row=3, column=id_col)
            id_cell.value = video_id
            if id_template:
                self.apply_column_format(id_cell, id_template)
                
            rate_cell = sheet.cell(row=3, column=rate_col)
            rate = float(rate)
            rate_cell.value = f"{rate:.2f}%"  # 保留2位小数并添加百分号
            if rate_template:
                self.apply_column_format(rate_cell, rate_template)
                
            print(f"完播率最高的视频 ID: {video_id}, 完播率: {rate:.2f}%")
            
        # 执行SQL查询 - 完播率最低的视频  
        lowest_result = self.execute_query(SQL_QUERIES["lowest_completion_rate"])
        if lowest_result and len(lowest_result['data']) > 0:
            video_id = lowest_result['data'][0][0]
            rate = lowest_result['data'][0][1]
            
            # 填写"完播率最低"行 (第4行，索引3)的数据
            id_cell = sheet.cell(row=4, column=id_col)
            id_cell.value = video_id
            if id_template:
                self.apply_column_format(id_cell, id_template)
                
            rate_cell = sheet.cell(row=4, column=rate_col)
            rate = float(rate)
            rate_cell.value = f"{rate:.2f}%"  # 保留2位小数并添加百分号
            if rate_template:
                self.apply_column_format(rate_cell, rate_template)
                
            print(f"完播率最低的视频 ID: {video_id}, 完播率: {rate:.2f}%")
                
    def process_all(self):
        """处理所有sheet页的数据"""
        print("开始处理Excel数据...")
        
        try:
            self.process_explore_statistics()
            self.process_marketing_card_model()
            self.process_recommended_parts_model()
            self.process_video_completion_rate()
            
            # 保存Excel文件
            self.workbook.save(self.result_excel_path)
            print(f"\nExcel数据处理完成，已保存到 {self.result_excel_path}")
        except Exception as e:
            print(f"处理Excel数据时发生错误: {e}")


# 主程序入口
if __name__ == "__main__":
    try:
        # 创建分析对象并处理数据
        analysis = ExploreAnalysis()
        analysis.process_all()
    except Exception as e:
        print(f"程序执行出错: {e}")

