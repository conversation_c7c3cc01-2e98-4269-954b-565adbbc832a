# Explore数据分析工具

该项目是一个用于分析Explore数据并将结果写入Excel文件的工具。程序通过TDengine REST API连接数据库，执行预定义的SQL查询，并将查询结果格式化后写入Excel文件。

## 功能特点

- 通过taosrest库连接TDengine数据库
- 执行多项数据分析查询，包括：
  - Explore页面的曝光次数、用户数、平均停留时长等统计
  - 营销卡片点击次数及相关Model统计
  - 视频播放及完播率统计
  - 推荐附件点击相关统计
- 自动维护Excel文件格式，保持样式一致性
- 自动复制模板文件并写入新的数据列
- 自动处理日期格式
- 支持测试模式，可使用模拟数据进行功能测试

## 文件结构

- `config.py` - 配置文件，包含TDengine连接信息和SQL查询语句
- `explore_analysis.py` - 主程序文件，连接TDengine数据库并执行查询
- `explore_analysis_test_improved.py` - 测试程序，使用模拟数据而非实际数据库连接
- `run.bat` - 运行主程序的批处理文件
- `run_test_improved.bat` - 运行测试程序的批处理文件
- `explore.xlsx` - Excel模板文件

## 使用方法

### 运行真实数据分析

1. 确保已安装Python（3.6及以上版本）
2. 双击`run.bat`运行主程序
3. 程序会自动安装所需的依赖库（如果缺少）
4. 程序连接TDengine数据库，执行查询并将结果写入新的Excel文件

### 运行测试版本（使用模拟数据）

1. 确保已安装Python（3.6及以上版本）
2. 双击`run_test_improved.bat`运行测试程序
3. 程序会生成模拟数据并将结果写入测试Excel文件

## 配置说明

如需修改配置参数，请编辑`config.py`文件：

- `TDENGINE_CONFIG` - TDengine连接配置
- `EXCEL_CONFIG` - Excel文件配置
- `SQL_QUERIES` - SQL查询语句配置

## 依赖库

- pandas
- openpyxl
- taosrest
