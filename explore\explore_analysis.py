

"""
Explore数据分析程序

该程序通过AWS Athena连接数据库，查询分析数据并将结果写入Excel文件。
"""

import os
import boto3
import time
import pandas as pd
from datetime import datetime
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
import shutil

# 导入配置
from config import ATHENA_CONFIG, EXCEL_CONFIG, SQL_QUERIES


class ExploreAnalysis:
    """Explore数据分析类"""

    def find_latest_result_file(self, source_excel_path):
        """
        查找最近的结果文件，按日期降序排序，返回最新的文件路径
        
        Args:
            source_excel_path: 源Excel文件路径，如果找不到结果文件将使用此文件
            
        Returns:
            最新的结果文件路径
        """
        # 获取当前目录下所有explore_开头的Excel文件
        explore_files = []
        current_dir = os.path.dirname(os.path.abspath(source_excel_path))
        
        for file in os.listdir(current_dir):
            if file.startswith("explore_") and file.endswith(".xlsx"):
                file_path = os.path.join(current_dir, file)
                
                # 尝试从文件名提取日期
                try:
                    # 文件名格式应为explore_YYYY-MM-DD.xlsx
                    date_str = file.replace("explore_", "").replace(".xlsx", "")
                    file_date = datetime.strptime(date_str, "%Y-%m-%d")
                    explore_files.append((file_path, file_date))
                except:
                    # 如果无法解析日期，跳过此文件
                    continue
        
        # 按日期降序排序
        explore_files.sort(key=lambda x: x[1], reverse=True)
        
        # 返回最新的文件，如果没有找到，则返回源文件
        if explore_files:
            latest_file = explore_files[0][0]
            print(f"找到最近的结果文件: {latest_file}")
            return latest_file
        else:
            print(f"没有找到历史结果文件，将使用源文件: {source_excel_path}")
            return source_excel_path
    
    def __init__(self, source_excel_path=None, result_excel_path=None):
        """
        初始化分析类
        
        Args:
            source_excel_path: 源Excel文件路径，如果为None则使用配置中的值
            result_excel_path: 结果Excel文件路径，如果为None则自动生成
        """
        # 设置源Excel文件路径
        if source_excel_path is None:
            source_excel_path = EXCEL_CONFIG["source_file"]
        self.source_excel_path = source_excel_path
        
        # 当前日期
        self.today = datetime.now().strftime('%Y-%m-%d')
        
        # 设置结果Excel文件路径
        if result_excel_path is None:
            if EXCEL_CONFIG["result_file"] is None:
                result_excel_path = f"explore_{self.today}.xlsx"
            else:
                result_excel_path = EXCEL_CONFIG["result_file"]
        self.result_excel_path = result_excel_path

        # 检查是否存在同日期的文件，如果存在则删除
        current_dir = os.path.dirname(os.path.abspath(source_excel_path))
        today_file = os.path.join(current_dir, f"explore_{self.today}.xlsx")
        if os.path.exists(today_file):
            try:
                # 尝试删除同日期文件
                os.remove(today_file)
                print(f"已删除同日期文件: {today_file}")
            except Exception as e:
                print(f"删除同日期文件失败: {e}")
                # 如果删除失败，使用带时间戳的文件名
                timestamp = datetime.now().strftime('%H%M%S')
                self.result_excel_path = self.result_excel_path.replace('.xlsx', f'_{timestamp}.xlsx')
                print(f"将使用新的文件名: {self.result_excel_path}")
        
        # 查找最近的结果文件作为基础文件
        base_file = self.find_latest_result_file(source_excel_path)
        
        # 检查基础文件是否存在
        if not os.path.exists(base_file):
            raise FileNotFoundError(f"Excel文件 {base_file} 不存在")
        
        # 如果基础文件与目标结果文件路径不同，则复制
        if base_file != self.result_excel_path:
            shutil.copy2(base_file, self.result_excel_path)
            print(f"已复制 {base_file} 到 {self.result_excel_path}")
        else:
            print(f"使用现有文件 {base_file}")
        
        # 加载工作簿
        self.workbook = load_workbook(self.result_excel_path)
        
        # 初始化AWS Athena客户端
        try:
            self.athena_client = boto3.client(
                'athena',
                aws_access_key_id=ATHENA_CONFIG["aws_access_key_id"],
                aws_secret_access_key=ATHENA_CONFIG["aws_secret_access_key"],
                region_name=ATHENA_CONFIG["region_name"]
            )
            print("AWS Athena连接成功")
            
            # 设置S3输出位置
            self.s3_output_location = ATHENA_CONFIG["s3_output_location"]
            self.database = ATHENA_CONFIG["database"]
        except Exception as e:
            raise ConnectionError(f"连接AWS Athena失败: {e}")
        
    def copy_cell_format(self, source_cell, target_cell):
        """
        复制单元格格式
        
        Args:
            source_cell: 源单元格
            target_cell: 目标单元格
        """
        # 简单复制字体属性
        target_cell.font = Font(
            name=source_cell.font.name,
            size=source_cell.font.size,
            bold=source_cell.font.bold,
            italic=source_cell.font.italic,
            underline=source_cell.font.underline,
            color=source_cell.font.color
        )
        
        # 复制对齐方式
        target_cell.alignment = Alignment(
            horizontal=source_cell.alignment.horizontal,
            vertical=source_cell.alignment.vertical,
            textRotation=source_cell.alignment.textRotation,
            wrapText=source_cell.alignment.wrapText
        )
        
        # 复制填充
        if source_cell.fill.fill_type:
            target_cell.fill = PatternFill(
                fill_type=source_cell.fill.fill_type,
                start_color=source_cell.fill.start_color,
                end_color=source_cell.fill.end_color
            )
        
        # 复制边框 (简化处理)
        if source_cell.border:
            target_cell.border = Border(
                left=source_cell.border.left,
                right=source_cell.border.right,
                top=source_cell.border.top,
                bottom=source_cell.border.bottom
            )
    
    def get_column_format_template(self, sheet):
        """
        获取列头格式模板单元格
        
        Args:
            sheet: 工作表对象
            
        Returns:
            模板单元格对象
        """
        template_cell = None
        
        # 查找第一个非空列头作为模板
        for col in range(2, sheet.max_column + 1):
            header_cell = sheet.cell(row=1, column=col)
            if header_cell.value:
                template_cell = header_cell
                break
        
        return template_cell
    
    def apply_column_format(self, cell, template_cell):
        """
        应用列头格式模板
        
        Args:
            cell: 要应用格式的单元格对象
            template_cell: 模板单元格对象
        """
        if not template_cell:
            return
        
        self.copy_cell_format(template_cell, cell)

    def get_date_format_from_cell(self, cell_value):
        """
        从单元格值获取日期格式
        
        Args:
            cell_value: 单元格值
            
        Returns:
            日期格式字符串, 实际显示格式
        """
        if not cell_value:
            return None, None
            
        # 将日期转换为字符串以检查格式
        if isinstance(cell_value, datetime):
            cell_str = cell_value.strftime("%Y/%m/%d")
        else:
            cell_str = str(cell_value)
            
        # 检查日期分隔符和格式
        if "/" in cell_str and len(cell_str.split("/")) == 3:
            parts = cell_str.split("/")
            # 检查月和日是否有前导零
            if len(parts[1]) == 1 and len(parts[2]) == 1:  # 2025/4/27格式
                return "%Y/%-m/%-d" if os.name != 'nt' else "%Y/%#m/%#d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            elif len(parts[1]) == 1:  # 2025/4/07格式
                return "%Y/%-m/%d" if os.name != 'nt' else "%Y/%#m/%d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            elif len(parts[2]) == 1:  # 2025/04/7格式
                return "%Y/%m/%-d" if os.name != 'nt' else "%Y/%m/%#d", f"{parts[0]}/{parts[1]}/{parts[2]}"
            else:  # 2025/04/07格式
                return "%Y/%m/%d", f"{parts[0]}/{parts[1]}/{parts[2]}"
        elif "-" in cell_str and len(cell_str.split("-")) == 3:
            return "%Y-%m-%d", cell_str
        elif "年" in cell_str and "月" in cell_str and "日" in cell_str:
            return "%Y年%m月%d日", cell_str
        
        return None, None

    def find_or_create_date_column(self, sheet):
        """
        查找或创建日期列，确保格式一致
        
        Args:
            sheet: 工作表对象
            
        Returns:
            日期列的索引
        """
        # 获取列头格式模板单元格
        format_template_cell = self.get_column_format_template(sheet)
        
        # 查找日期列
        date_col = None
        date_format = None
        display_format = None
        
        # 查找现有日期列的格式
        for col in range(2, sheet.max_column + 1):
            header = sheet.cell(row=1, column=col).value
            if header:
                # 尝试识别日期格式
                fmt, disp = self.get_date_format_from_cell(header)
                if fmt:
                    date_format = fmt
                    display_format = disp
                    break
        
        # 如果找不到日期格式，使用默认格式 (2025/4/14，没有前导零)
        if not date_format:
            date_format = "%Y/%#m/%#d" if os.name == 'nt' else "%Y/%-m/%-d"
            now = datetime.now()
            display_format = f"{now.year}/{now.month}/{now.day}"
        
        # 使用识别出的格式生成今天的日期
        now = datetime.now()
        if display_format:
            # 直接按照显示格式构建字符串，维持与已有日期相同的格式
            parts = display_format.split('/')
            if len(parts) == 3:
                # 确保和历史格式保持一致
                year_fmt = parts[0].replace(str(int(parts[0])), '%Y')
                month_fmt = parts[1].replace(str(int(parts[1])), '%m' if len(parts[1]) == 2 else '%#m' if os.name == 'nt' else '%-m')
                day_fmt = parts[2].replace(str(int(parts[2])), '%d' if len(parts[2]) == 2 else '%#d' if os.name == 'nt' else '%-d')
                
                # 检查展示格式中月日是否有前导零
                has_month_zero = len(parts[1]) == 2 and parts[1].startswith('0')
                has_day_zero = len(parts[2]) == 2 and parts[2].startswith('0')
                
                # 根据历史格式决定是否添加前导零
                month_str = f"{now.month:02d}" if has_month_zero else f"{now.month}"
                day_str = f"{now.day:02d}" if has_day_zero else f"{now.day}"
                today_formatted = f"{now.year}/{month_str}/{day_str}"
            else:
                today_formatted = f"{now.year}/{now.month}/{now.day}"
        else:
            # 使用strftime格式化（不推荐，因为Windows不支持某些格式符）
            try:
                today_formatted = now.strftime(date_format)
            except:
                today_formatted = f"{now.year}/{now.month}/{now.day}"
                
        print(f"识别到的日期格式: {date_format}, 显示格式: {display_format}")
        print(f"格式化后的今天日期: {today_formatted}")
        
        # 查找最后一个非空列和第一个空列
        last_non_empty_col = 1
        next_empty_col = None
        
        # 从右向左找最后一个非空列
        for col in range(sheet.max_column, 0, -1):
            col_has_data = False
            for row in range(1, min(10, sheet.max_row + 1)):  # 检查前几行
                if sheet.cell(row=row, column=col).value is not None:
                    col_has_data = True
                    last_non_empty_col = col
                    break
            if col_has_data:
                break
                
        # 下一个可用列就是最后非空列的后一列
        next_empty_col = last_non_empty_col + 1
        
        # 查找是否已存在今天的日期列
        date_col = None
        for col in range(1, last_non_empty_col + 1):
            header = sheet.cell(row=1, column=col).value
            if header:
                header_str = str(header)
                today_str = today_formatted
                
                # 尝试将两者解析为日期对象来比较
                try:
                    header_date = datetime.strptime(header_str, date_format)
                    today_date = datetime.strptime(today_str, date_format)
                    if header_date.date() == today_date.date():
                        date_col = col
                        print(f"找到现有日期列 {date_col}: {header_str}")
                        break
                except:
                    # 直接比较字符串
                    if header_str == today_str:
                        date_col = col
                        print(f"找到现有日期列 {date_col}: {header_str}")
                        break
        
        # 如果没有找到今天的日期列，则创建新列
        if date_col is None:
            date_col = next_empty_col
            header_cell = sheet.cell(row=1, column=date_col)
            header_cell.value = today_formatted
            
            # 直接从现有日期列复制完整格式
            template_date_cell = None
            for col in range(2, sheet.max_column):
                if isinstance(sheet.cell(row=1, column=col).value, (str, datetime)) and col != date_col:
                    template_date_cell = sheet.cell(row=1, column=col)
                    break
            
            if template_date_cell:
                self.copy_cell_format(template_date_cell, header_cell)
            else:
                self.apply_column_format(header_cell, format_template_cell)
                
            print(f"在列 {date_col} 创建新的日期列: {today_formatted}")
        
        return date_col
    
    def execute_query(self, sql):
        """
        执行AWS Athena SQL查询
        
        Args:
            sql: SQL查询语句
            
        Returns:
            查询结果，格式与TDengine兼容: {'data': [[value1], [value2], ...]}
        """
        try:
            print(f"执行AWS Athena查询: {sql[:80]}...")  # 只打印前80个字符，避免日志过长
            
            # 启动查询
            response = self.athena_client.start_query_execution(
                QueryString=sql,
                QueryExecutionContext={
                    'Database': self.database
                },
                ResultConfiguration={
                    'OutputLocation': self.s3_output_location,
                }
            )
            
            # 获取查询执行ID
            query_execution_id = response['QueryExecutionId']
            print(f"查询ID: {query_execution_id}")
            
            # 等待查询完成
            state = 'RUNNING'
            max_attempts = 150  # 设置最大尝试次数，避免无限循环
            attempts = 0
            
            while state in ['RUNNING', 'QUEUED'] and attempts < max_attempts:
                response = self.athena_client.get_query_execution(
                    QueryExecutionId=query_execution_id
                )
                state = response['QueryExecution']['Status']['State']
                
                if state in ['RUNNING', 'QUEUED']:
                    print(f"查询状态: {state}，等待...")
                    time.sleep(1)  # 等待1秒后再检查
                    attempts += 1
            
            # 检查查询状态
            if state == 'SUCCEEDED':
                # 获取查询结果
                response = self.athena_client.get_query_results(
                    QueryExecutionId=query_execution_id
                )
                
                # 解析结果，转换为与TDengine兼容的格式
                rows = []
                for row in response['ResultSet']['Rows'][1:]:  # 跳过标题行
                    # 提取每行的实际值
                    values = [col.get('VarCharValue', None) for col in row['Data']]
                    rows.append(values)
                
                print(f"查询成功，返回 {len(rows)} 条结果")
                return {"data": rows}
            else:
                print(f"查询失败，状态: {state}")
                if 'StateChangeReason' in response['QueryExecution']['Status']:
                    print(f"原因: {response['QueryExecution']['Status']['StateChangeReason']}")
                return {"data": []}
                
        except Exception as e:
            print(f"查询执行失败: {e}")
            return {"data": []}
    
    def get_cell_template(self, sheet, start_row, end_row, start_col=2):
        """
        获取单元格格式模板单元格

        Args:
            sheet: 工作表对象
            start_row: 起始行
            end_row: 结束行
            start_col: 起始列

        Returns:
            模板单元格对象
        """
        template_cell = None
        # 查找第一个非空数据单元格作为模板
        for col in range(start_col, sheet.max_column + 1):
            for row in range(start_row, end_row + 1):
                data_cell = sheet.cell(row=row, column=col)
                if data_cell.value is not None:
                    template_cell = data_cell
                    break
            if template_cell:
                break
        return template_cell

    def find_historical_template(self, sheet, row, date_col):
        """
        查找该行的历史数据单元格作为格式模板

        Args:
            sheet: 工作表对象
            row: 行号
            date_col: 当前日期列

        Returns:
            历史模板单元格对象或None
        """
        for hist_col in range(2, date_col):
            if sheet.cell(row=row, column=hist_col).value is not None:
                return sheet.cell(row=row, column=hist_col)
        return None

    def apply_cell_formatting(self, cell, historical_template, cell_template):
        """
        统一的单元格格式应用逻辑

        Args:
            cell: 要应用格式的单元格
            historical_template: 历史模板单元格
            cell_template: 通用模板单元格
        """
        if historical_template:
            self.copy_cell_format(historical_template, cell)
        elif cell_template:
            self.apply_column_format(cell, cell_template)

    def get_existing_items(self, sheet, start_row=2):
        """
        获取现有项目列表（通用方法）

        Args:
            sheet: 工作表对象
            start_row: 起始行号，默认为2

        Returns:
            字典，键为项目名称，值为行号
        """
        existing_items = {}
        for row in range(start_row, sheet.max_row + 1):
            item = sheet.cell(row=row, column=1).value
            if item:
                existing_items[str(item)] = row
        return existing_items

    def find_last_data_row(self, sheet, start_row=2):
        """
        查找最后一个有数据的行

        Args:
            sheet: 工作表对象
            start_row: 起始行号，默认为2

        Returns:
            最后一个有数据的行号
        """
        current_row = start_row
        last_data_row = start_row - 1
        while current_row <= sheet.max_row:
            if sheet.cell(row=current_row, column=1).value is not None:
                last_data_row = current_row
            current_row += 1
        return last_data_row

    def initialize_existing_items_to_zero(self, sheet, existing_items, date_col, cell_template):
        """
        初始化所有现有项目的当天数据为0

        Args:
            sheet: 工作表对象
            existing_items: 现有项目字典
            date_col: 日期列
            cell_template: 单元格模板
        """
        for item, row in existing_items.items():
            cell = sheet.cell(row=row, column=date_col)
            # 检查是否是合并单元格，如果是则跳过
            if not self.is_merged_cell(sheet, row, date_col):
                cell.value = 0
                if cell_template:
                    self.apply_column_format(cell, cell_template)

    def add_border_to_cell(self, cell):
        """
        为单元格添加实线边框

        Args:
            cell: 单元格对象
        """
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    def fill_historical_zeros(self, sheet, row, date_col, cell_template, skip_cols=None):
        """
        为新项目在所有历史日期列中填充0值

        Args:
            sheet: 工作表对象
            row: 行号
            date_col: 当前日期列
            cell_template: 单元格模板
            skip_cols: 要跳过的列集合
        """
        skip_cols = skip_cols or set()
        for col in range(2, sheet.max_column + 1):
            if col != date_col and col not in skip_cols and sheet.cell(row=1, column=col).value is not None:
                header = sheet.cell(row=1, column=col).value
                if isinstance(header, (datetime, str)):
                    hist_cell = sheet.cell(row=row, column=col)
                    # 检查是否是合并单元格，如果是则跳过
                    if not self.is_merged_cell(sheet, row, col):
                        hist_cell.value = 0
                        if cell_template:
                            self.apply_column_format(hist_cell, cell_template)

    def is_merged_cell(self, sheet, row, col):
        """
        检查单元格是否是合并单元格的一部分（非左上角）

        Args:
            sheet: 工作表对象
            row: 行号
            col: 列号

        Returns:
            bool: 如果是合并单元格的非左上角部分返回True，否则返回False
        """
        try:
            cell = sheet.cell(row=row, column=col)
            # 检查单元格类型
            from openpyxl.cell.cell import MergedCell
            return isinstance(cell, MergedCell)
        except Exception:
            return False
            
    def process_explore_statistics(self):
        """处理【Explore数据统计】sheet页数据"""
        print("\n开始处理【Explore数据统计】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["Explore数据统计"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=13)
        
        # 执行13个SQL查询
        for i, sql in enumerate(SQL_QUERIES["explore_statistics"]):
            try:
                result = self.execute_query(sql)
                
                if result and len(result['data']) > 0:
                    value = result['data'][0][0]
                    cell = sheet.cell(row=i+2, column=date_col)
                    
                    # 序号3（Explore页面平均停留时长）的值需要四舍五入保留0位小数
                    if i == 2:  # 索引从0开始，对应序号3
                        try:
                            # 安全转换：确保value是数字类型
                            if isinstance(value, str):
                                # 移除可能的非数字字符
                                value = value.replace(',', '').strip()
                                if not value:  # 如果是空字符串，设为0
                                    value = 0
                            value = float(value)  # 转换为浮点数
                            value = round(value, 0)  # 四舍五入保留0位小数
                            value = int(value)  # 确保显示为整数
                        except (ValueError, TypeError) as e:
                            print(f"处理序号3时类型转换出错: {e}，原值: {value}，使用默认值0")
                            value = 0
                        
                    # 序号10（所有视频平均完播率）的值需要存为xx%格式
                    elif i == 9:  # 索引从0开始，对应序号10
                        try:
                            if isinstance(value, str):
                                # 检查值是否已经包含百分号
                                if "%" not in value:
                                    # 移除可能的非数字字符
                                    value = value.replace(',', '').strip()
                                    if not value:  # 如果是空字符串，设为0
                                        value = 0
                                    value = float(value)  # 转换为浮点数
                                    value = f"{value:.2f}%"  # 保留2位小数并添加百分号
                                # 如果已经有百分号，保持原样
                            else:
                                # 如果是数值类型，直接格式化
                                value = float(value)  # 转换为浮点数
                                value = f"{value:.2f}%"  # 保留2位小数并添加百分号
                        except (ValueError, TypeError) as e:
                            print(f"处理序号10时类型转换出错: {e}，原值: {value}，使用默认值0%")
                            value = "0.00%"
                        
                    if not self.is_merged_cell(sheet, i+2, date_col):
                        cell.value = value
                        # 使用公共方法应用格式
                        historical_template = self.find_historical_template(sheet, i+2, date_col)
                        self.apply_cell_formatting(cell, historical_template, cell_template)
                    else:
                        print(f"跳过合并单元格，序号 {i+1}")
                        
                    print(f"序号 {i+1} 查询结果: {value}")
                else:
                    cell = sheet.cell(row=i+2, column=date_col)
                    if not self.is_merged_cell(sheet, i+2, date_col):
                        cell.value = 0
                        # 使用公共方法应用格式
                        historical_template = self.find_historical_template(sheet, i+2, date_col)
                        self.apply_cell_formatting(cell, historical_template, cell_template)
                    else:
                        print(f"跳过合并单元格，序号 {i+1} 无结果填充")
                        
                    print(f"序号 {i+1} 查询无结果，填充0")
            except Exception as e:
                print(f"处理序号 {i+1} 时出错: {e}")
                cell = sheet.cell(row=i+2, column=date_col)
                if not self.is_merged_cell(sheet, i+2, date_col):
                    cell.value = "ERROR"
                    # 使用公共方法应用格式
                    historical_template = self.find_historical_template(sheet, i+2, date_col)
                    self.apply_cell_formatting(cell, historical_template, cell_template)
                else:
                    print(f"跳过合并单元格，序号 {i+1} 错误处理")
                
        # 处理序号14和15的公式计算
        # 序号14: 序号5的值 / 序号2的值 * 100，保留2位小数
        cell_14 = sheet.cell(row=15, column=date_col)
        cell_5 = sheet.cell(row=6, column=date_col).value  # 序号5对应第6行
        cell_2 = sheet.cell(row=3, column=date_col).value  # 序号2对应第3行
        
        # 确保进行类型转换
        try:
            # 将单元格值转换为浮点数
            val_5 = float(cell_5) if cell_5 and cell_5 != "ERROR" else 0
            val_2 = float(cell_2) if cell_2 and cell_2 != "ERROR" else 0
            
            if val_2 > 0:
                result = (val_5 / val_2) * 100
                if not self.is_merged_cell(sheet, 15, date_col):
                    cell_14.value = round(result, 2)  # 保留2位小数
                    # 使用公共方法应用格式
                    historical_template = self.find_historical_template(sheet, 15, date_col)
                    self.apply_cell_formatting(cell_14, historical_template, cell_template)
                else:
                    print("跳过合并单元格，序号 14 计算结果")
                    
                print(f"序号 14 计算结果: {cell_14.value}")
            else:
                if not self.is_merged_cell(sheet, 15, date_col):
                    cell_14.value = 0
                    # 使用公共方法应用格式
                    historical_template = self.find_historical_template(sheet, 15, date_col)
                    self.apply_cell_formatting(cell_14, historical_template, cell_template)
                else:
                    print("跳过合并单元格，序号 14 填充0")
                    
                print("序号 14 计算失败（除数为0），填充0")
        except Exception as e:
            print(f"计算序号 14 时出错: {e}")
            if not self.is_merged_cell(sheet, 15, date_col):
                cell_14.value = 0
                # 使用公共方法应用格式
                historical_template = self.find_historical_template(sheet, 15, date_col)
                self.apply_cell_formatting(cell_14, historical_template, cell_template)
            else:
                print("跳过合并单元格，序号 14 错误处理")
            
        # 序号15: 序号12的值 / 序号11的值 * 100，保留2位小数
        cell_15 = sheet.cell(row=16, column=date_col)
        cell_12 = sheet.cell(row=13, column=date_col).value  # 序号12对应第13行
        cell_11 = sheet.cell(row=12, column=date_col).value  # 序号11对应第12行
        
        # 确保进行类型转换
        try:
            # 将单元格值转换为浮点数
            val_12 = float(cell_12) if cell_12 and cell_12 != "ERROR" else 0
            val_11 = float(cell_11) if cell_11 and cell_11 != "ERROR" else 0
            
            if val_11 > 0:
                result = (val_12 / val_11) * 100
                if not self.is_merged_cell(sheet, 16, date_col):
                    cell_15.value = round(result, 2)  # 保留2位小数
                    # 使用公共方法应用格式
                    historical_template = self.find_historical_template(sheet, 16, date_col)
                    self.apply_cell_formatting(cell_15, historical_template, cell_template)
                else:
                    print("跳过合并单元格，序号 15 计算结果")
                    
                print(f"序号 15 计算结果: {cell_15.value}")
            else:
                if not self.is_merged_cell(sheet, 16, date_col):
                    cell_15.value = 0
                    # 使用公共方法应用格式
                    historical_template = self.find_historical_template(sheet, 16, date_col)
                    self.apply_cell_formatting(cell_15, historical_template, cell_template)
                else:
                    print("跳过合并单元格，序号 15 填充0")
                    
                print("序号 15 计算失败（除数为0），填充0")
        except Exception as e:
            print(f"计算序号 15 时出错: {e}")
            if not self.is_merged_cell(sheet, 16, date_col):
                cell_15.value = 0
                # 使用公共方法应用格式
                historical_template = self.find_historical_template(sheet, 16, date_col)
                self.apply_cell_formatting(cell_15, historical_template, cell_template)
            else:
                print("跳过合并单元格，序号 15 错误处理")
            
    def process_marketing_card_model(self):
        """处理【营销卡片（点击）Model】sheet页数据"""
        print("\n开始处理【营销卡片（点击）Model】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["营销卡片（点击）Model"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["marketing_card_model"])
        
        if not result or len(result['data']) == 0:
            print("营销卡片查询无结果")
            return
                
        # 使用公共方法获取现有model列表
        existing_models = self.get_existing_items(sheet, start_row=2)

        # 使用公共方法初始化所有现有model的当天数据为0
        self.initialize_existing_items_to_zero(sheet, existing_models, date_col, cell_template)
                
        # 使用公共方法找到最后一个数据行
        current_row = self.find_last_data_row(sheet, start_row=2) + 1
        for model_data in result['data']:
            model_number = model_data[0]
            cn_value = model_data[1]
            
            if str(model_number) in existing_models:
                # 更新现有model
                row = existing_models[str(model_number)]
                cell = sheet.cell(row=row, column=date_col)
                if not self.is_merged_cell(sheet, row, date_col):
                    cell.value = cn_value
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"更新营销卡片Model: {model_number}, 值: {cn_value}")
                else:
                    print(f"跳过合并单元格，无法更新营销卡片Model: {model_number}")
            else:
                # 添加新model
                model_cell = sheet.cell(row=current_row, column=1)
                if not self.is_merged_cell(sheet, current_row, 1):
                    model_cell.value = model_number
                    # 使用公共方法添加边框
                    self.add_border_to_cell(model_cell)

                # 使用公共方法为新model在所有历史日期列中填充0
                self.fill_historical_zeros(sheet, current_row, date_col, cell_template)

                # 在当前日期列填写实际值
                cell = sheet.cell(row=current_row, column=date_col)
                if not self.is_merged_cell(sheet, current_row, date_col):
                    cell.value = cn_value
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"添加新营销卡片Model: {model_number}, 值: {cn_value}")
                else:
                    print(f"跳过合并单元格，无法添加营销卡片Model: {model_number}")

                current_row += 1
            
    def process_recommended_parts_model(self):
        """处理【推荐附件（点击）Model】sheet页数据"""
        print("\n开始处理【推荐附件（点击）Model】sheet页数据...")
        
        # 获取工作表
        sheet = self.workbook["推荐附件（点击）Model"]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["recommended_parts_model"])
        
        if not result or len(result['data']) == 0:
            print("推荐附件查询无结果")
            return
                
        # 使用公共方法获取现有model列表
        existing_models = self.get_existing_items(sheet, start_row=2)

        # 使用公共方法初始化所有现有model的当天数据为0
        self.initialize_existing_items_to_zero(sheet, existing_models, date_col, cell_template)
                
        # 使用公共方法找到最后一个数据行
        current_row = self.find_last_data_row(sheet, start_row=2) + 1
        for model_data in result['data']:
            model_number = model_data[0]
            cn_value = model_data[1]
            
            if str(model_number) in existing_models:
                # 更新现有model
                row = existing_models[str(model_number)]
                cell = sheet.cell(row=row, column=date_col)
                if not self.is_merged_cell(sheet, row, date_col):
                    cell.value = cn_value
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"更新推荐附件Model: {model_number}, 值: {cn_value}")
                else:
                    print(f"跳过合并单元格，无法更新推荐附件Model: {model_number}")
            else:
                # 添加新model
                model_cell = sheet.cell(row=current_row, column=1)
                if not self.is_merged_cell(sheet, current_row, 1):
                    model_cell.value = model_number
                    # 使用公共方法添加边框
                    self.add_border_to_cell(model_cell)

                # 使用公共方法为新model在所有历史日期列中填充0
                self.fill_historical_zeros(sheet, current_row, date_col, cell_template)

                # 在当前日期列填写实际值
                cell = sheet.cell(row=current_row, column=date_col)
                if not self.is_merged_cell(sheet, current_row, date_col):
                    cell.value = cn_value
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"添加新推荐附件Model: {model_number}, 值: {cn_value}")
                else:
                    print(f"跳过合并单元格，无法添加推荐附件Model: {model_number}")

                current_row += 1
            
    def process_video_playback_completion_rate(self):
        """处理【视频播放量及平均完播率】sheet页数据"""
        print("\n开始处理【视频播放量及平均完播率】sheet页数据...")
        
        # 获取工作表
        sheet_name = "视频播放量及平均完播率"
        # 检查sheet是否存在，如果不存在则创建
        if sheet_name not in self.workbook.sheetnames:
            self.workbook.create_sheet(sheet_name)
            print(f"创建新sheet页: {sheet_name}")
        
        sheet = self.workbook[sheet_name]
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 确保第一行和第二行有标题
        # 第一行：视频ID, 第二行：播放量，完播率
        if not sheet.cell(row=1, column=1).value:
            if not self.is_merged_cell(sheet, 1, 1):
                sheet.cell(row=1, column=1).value = "视频ID"
        
        # 确保播放量和完播率列存在，需要和date_col列对应
        play_col = date_col
        rate_col = date_col + 1
        
        # 获取日期列表格式模板
        date_template = self.get_column_format_template(sheet)
        
        # 设置日期标题
        date_header = sheet.cell(row=1, column=play_col)
        date_header2 = sheet.cell(row=1, column=rate_col)
        
        # 获取当前日期的格式化字符串
        now = datetime.now()
        today_formatted = f"{now.year}/{now.month}/{now.day}"
        
        # 设置日期标题在第一列并合并单元格
        if not self.is_merged_cell(sheet, 1, play_col):
            date_header.value = today_formatted
        if not self.is_merged_cell(sheet, 1, rate_col):
            date_header2.value = None
        
        if date_template:
            self.apply_column_format(date_header, date_template)
        
        # 合并日期单元格，使其横跨两列
        try:
            merge_range = f"{get_column_letter(play_col)}1:{get_column_letter(rate_col)}1"
            sheet.merge_cells(merge_range)
            print(f"合并日期单元格: {merge_range}")
        except Exception as e:
            print(f"合并单元格时出错: {e}")
        
        # 设置播放量和完播率作为子标题（第2行）
        play_header = sheet.cell(row=2, column=play_col)
        if not self.is_merged_cell(sheet, 2, play_col):
            play_header.value = "播放量"

        rate_header = sheet.cell(row=2, column=rate_col)
        if not self.is_merged_cell(sheet, 2, rate_col):
            rate_header.value = "完播率"
        
        if date_template:
            self.apply_column_format(play_header, date_template)
            self.apply_column_format(rate_header, date_template)
        
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=3, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["video_playback_completion_rate"])
        
        if not result or len(result['data']) == 0:
            print("视频播放及完播率查询无结果")
            return
        
        # 使用公共方法获取现有视频ID列表
        existing_videos = self.get_existing_items(sheet, start_row=3)

        # 初始化所有现有视频ID的当天数据为0，这样如果某个视频在当天没有数据，就会显示为0
        for video_id, row in existing_videos.items():
            # 设置播放量为0
            play_cell = sheet.cell(row=row, column=play_col)
            if not self.is_merged_cell(sheet, row, play_col):
                play_cell.value = 0
                if cell_template:
                    self.apply_column_format(play_cell, cell_template)

            # 设置完播率为0%
            rate_cell = sheet.cell(row=row, column=rate_col)
            if not self.is_merged_cell(sheet, row, rate_col):
                rate_cell.value = "0.00%"
                if cell_template:
                    self.apply_column_format(rate_cell, cell_template)
                
        # 使用公共方法找到最后一个数据行
        current_row = self.find_last_data_row(sheet, start_row=3) + 1
        
        # 处理每个视频的数据
        for video_data in result['data']:
            video_id = video_data[0]
            play_count = video_data[1]
            play_rate = video_data[2]  # 格式应为 "xx.xx%"
            
            if str(video_id) in existing_videos:
                # 更新现有视频数据
                row = existing_videos[str(video_id)]
                
                # 设置播放量
                play_cell = sheet.cell(row=row, column=play_col)
                if not self.is_merged_cell(sheet, row, play_col):
                    play_cell.value = play_count
                    if cell_template:
                        self.apply_column_format(play_cell, cell_template)

                # 设置完播率
                rate_cell = sheet.cell(row=row, column=rate_col)
                if not self.is_merged_cell(sheet, row, rate_col):
                    rate_cell.value = play_rate
                    if cell_template:
                        self.apply_column_format(rate_cell, cell_template)
                
                print(f"更新视频ID: {video_id}, 播放量: {play_count}, 完播率: {play_rate}")
            else:
                # 添加新视频
                # 设置视频ID
                id_cell = sheet.cell(row=current_row, column=1)
                if not self.is_merged_cell(sheet, current_row, 1):
                    id_cell.value = video_id
                    # 使用公共方法添加边框
                    self.add_border_to_cell(id_cell)

                # 为新视频ID在所有历史日期列中填充0
                for col in range(2, sheet.max_column + 1, 2):  # 每次跳过2列，因为是播放量和完播率
                    if col != play_col and sheet.cell(row=1, column=col).value is not None:
                        # 检查该列是否是日期列
                        header = sheet.cell(row=1, column=col).value
                        if isinstance(header, (datetime, str)):
                            # 填充0值 - 播放量
                            hist_play_cell = sheet.cell(row=current_row, column=col)
                            if not self.is_merged_cell(sheet, current_row, col):
                                hist_play_cell.value = 0
                                if cell_template:
                                    self.apply_column_format(hist_play_cell, cell_template)

                            # 填充0% - 完播率
                            hist_rate_cell = sheet.cell(row=current_row, column=col+1)
                            if not self.is_merged_cell(sheet, current_row, col+1):
                                hist_rate_cell.value = "0.00%"
                                if cell_template:
                                    self.apply_column_format(hist_rate_cell, cell_template)
                
                # 在当前日期列填写实际值
                # 设置播放量
                play_cell = sheet.cell(row=current_row, column=play_col)
                if not self.is_merged_cell(sheet, current_row, play_col):
                    play_cell.value = play_count
                    if cell_template:
                        self.apply_column_format(play_cell, cell_template)

                # 设置完播率
                rate_cell = sheet.cell(row=current_row, column=rate_col)
                if not self.is_merged_cell(sheet, current_row, rate_col):
                    rate_cell.value = play_rate
                    if cell_template:
                        self.apply_column_format(rate_cell, cell_template)
                
                print(f"添加新视频ID: {video_id}, 播放量: {play_count}, 完播率: {play_rate}")
                current_row += 1
    
    def process_photo_clicks(self):
        """处理【图片点击量】sheet页数据"""
        print("\n开始处理【图片点击量】sheet页数据...")
        
        # 获取工作表
        sheet_name = "图片点击量"
        # 检查sheet是否存在，如果不存在则创建
        if sheet_name not in self.workbook.sheetnames:
            self.workbook.create_sheet(sheet_name)
            print(f"创建新sheet页: {sheet_name}")
        
        sheet = self.workbook[sheet_name]
        
        # 先从现有日期列获取格式模板
        template_date_cell = None
        for col in range(2, sheet.max_column + 1):
            header_cell = sheet.cell(row=1, column=col)
            if isinstance(header_cell.value, (str, datetime)):
                template_date_cell = header_cell
                break
        
        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)
        
        # 确保第一行有标题
        if not sheet.cell(row=1, column=1).value:
            # 检查是否是合并单元格
            if not self.is_merged_cell(sheet, 1, 1):
                sheet.cell(row=1, column=1).value = "图片ID"
        
        # 如果有模板日期单元格，直接复制其格式到新日期单元格
        date_header = sheet.cell(row=1, column=date_col)
        if template_date_cell:
            self.copy_cell_format(template_date_cell, date_header)
        
        # 获取日期列表格式模板
        date_template = self.get_column_format_template(sheet) 
        
        # 设置点击量作为子标题（第2行）
        click_header = sheet.cell(row=2, column=date_col)
        if not self.is_merged_cell(sheet, 2, date_col):
            click_header.value = "点击量"
            if date_template:
                self.apply_column_format(click_header, date_template)
            
        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)
        
        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["photo_clicks"])
        
        if not result or len(result['data']) == 0:
            print("图片点击量查询无结果")
            return
        
        # 使用公共方法获取现有图片ID列表
        existing_images = self.get_existing_items(sheet, start_row=2)

        # 使用公共方法初始化所有现有图片ID的当天数据为0
        self.initialize_existing_items_to_zero(sheet, existing_images, date_col, cell_template)
        
        # 使用公共方法找到最后一个数据行
        current_row = self.find_last_data_row(sheet, start_row=2) + 1
        
        for image_data in result['data']:
            image_id = image_data[0]
            click_count = image_data[1]
            
            if str(image_id) in existing_images:
                # 更新现有图片点击数据
                row = existing_images[str(image_id)]
                cell = sheet.cell(row=row, column=date_col)
                if not self.is_merged_cell(sheet, row, date_col):
                    cell.value = click_count
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    print(f"更新图片ID: {image_id}, 点击量: {click_count}")
                else:
                    print(f"跳过合并单元格，无法更新图片ID: {image_id}")
            else:
                # 添加新图片
                # 设置图片ID
                id_cell = sheet.cell(row=current_row, column=1)
                if not self.is_merged_cell(sheet, current_row, 1):
                    id_cell.value = image_id
                    # 使用公共方法添加边框
                    self.add_border_to_cell(id_cell)

                # 使用公共方法为新图片ID在所有历史日期列中填充0
                self.fill_historical_zeros(sheet, current_row, date_col, cell_template)

                # 设置点击量
                click_cell = sheet.cell(row=current_row, column=date_col)
                if not self.is_merged_cell(sheet, current_row, date_col):
                    click_cell.value = click_count
                    if cell_template:
                        self.apply_column_format(click_cell, cell_template)
                    print(f"添加新图片ID: {image_id}, 点击量: {click_count}")
                else:
                    print(f"跳过合并单元格，无法添加图片ID: {image_id}")

                current_row += 1

    def process_user_funnel(self):
        """处理【用户漏斗】sheet页数据"""
        print("\n开始处理【用户漏斗】sheet页数据...")

        # 获取工作表
        sheet_name = "用户漏斗"
        # 检查sheet是否存在，如果不存在则创建
        if sheet_name not in self.workbook.sheetnames:
            self.workbook.create_sheet(sheet_name)
            print(f"创建新sheet页: {sheet_name}")

        sheet = self.workbook[sheet_name]

        # 查找或创建日期列，确保格式一致
        date_col = self.find_or_create_date_column(sheet)

        # 确保第一行有标题
        if not sheet.cell(row=1, column=1).value:
            if not self.is_merged_cell(sheet, 1, 1):
                sheet.cell(row=1, column=1).value = "用户类型"
                # 使用公共方法为标题单元格添加边框
                self.add_border_to_cell(sheet.cell(row=1, column=1))

        # 使用公共方法为日期标题添加边框
        date_header = sheet.cell(row=1, column=date_col)
        self.add_border_to_cell(date_header)

        # 获取单元格格式模板
        cell_template = self.get_cell_template(sheet, start_row=2, end_row=sheet.max_row)

        # 执行SQL查询
        result = self.execute_query(SQL_QUERIES["user_funnel"])

        if not result or len(result['data']) == 0:
            print("用户漏斗查询无结果")
            return

        # 使用公共方法获取现有用户类型
        existing_types = self.get_existing_items(sheet, start_row=2)

        # 初始化所有现有用户类型的当天数据为0
        for user_type, row in existing_types.items():
            cell = sheet.cell(row=row, column=date_col)
            if not self.is_merged_cell(sheet, row, date_col):
                cell.value = 0
                if cell_template:
                    self.apply_column_format(cell, cell_template)
                # 使用公共方法添加边框
                self.add_border_to_cell(cell)

        # 使用公共方法找到最后一个数据行
        current_row = self.find_last_data_row(sheet, start_row=2) + 1

        for user_data in result['data']:
            user_type = user_data[0]  # T1, T2, T3
            user_count = user_data[1]  # 用户数量

            if str(user_type) in existing_types:
                # 更新现有用户类型数据
                row = existing_types[str(user_type)]
                cell = sheet.cell(row=row, column=date_col)
                if not self.is_merged_cell(sheet, row, date_col):
                    cell.value = user_count
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    # 使用公共方法添加边框
                    self.add_border_to_cell(cell)
                    print(f"更新用户类型: {user_type}, 数量: {user_count}")
                else:
                    print(f"跳过合并单元格，无法更新用户类型: {user_type}")
            else:
                # 添加新用户类型
                type_cell = sheet.cell(row=current_row, column=1)
                if not self.is_merged_cell(sheet, current_row, 1):
                    type_cell.value = user_type
                    # 使用公共方法添加边框
                    self.add_border_to_cell(type_cell)

                # 为新用户类型在所有历史日期列中填充0
                for col in range(2, sheet.max_column + 1):
                    if col != date_col and sheet.cell(row=1, column=col).value is not None:
                        # 检查该列是否是日期列
                        header = sheet.cell(row=1, column=col).value
                        if isinstance(header, (datetime, str)):
                            # 填充0值
                            hist_cell = sheet.cell(row=current_row, column=col)
                            if not self.is_merged_cell(sheet, current_row, col):
                                hist_cell.value = 0
                                if cell_template:
                                    self.apply_column_format(hist_cell, cell_template)
                                # 使用公共方法添加边框
                                self.add_border_to_cell(hist_cell)

                # 在当前日期列填写实际值
                cell = sheet.cell(row=current_row, column=date_col)
                if not self.is_merged_cell(sheet, current_row, date_col):
                    cell.value = user_count
                    if cell_template:
                        self.apply_column_format(cell, cell_template)
                    # 使用公共方法添加边框
                    self.add_border_to_cell(cell)

                print(f"添加新用户类型: {user_type}, 数量: {user_count}")
                current_row += 1

    def process_all(self):
        """处理所有sheet页的数据"""
        print("开始处理Excel数据...")
        
        try:
            self.process_explore_statistics()
            self.process_marketing_card_model()
            self.process_recommended_parts_model()
            self.process_video_playback_completion_rate()
            self.process_photo_clicks()
            # self.process_user_funnel()
            
            # 保存Excel文件
            self.workbook.save(self.result_excel_path)
            print(f"\nExcel数据处理完成，已保存到 {self.result_excel_path}")
        except Exception as e:
            print(f"处理Excel数据时发生错误: {e}")


# 主程序入口
if __name__ == "__main__":
    try:
        # 创建分析对象并处理数据
        analysis = ExploreAnalysis()
        analysis.process_all()
    except Exception as e:
        print(f"程序执行出错: {e}")

