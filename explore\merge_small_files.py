import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from datetime import datetime, timedelta
import boto3
import json

class CheckpointManager:
    def __init__(self, checkpoint_path):
        """初始化checkpoint管理器"""
        self.checkpoint_path = checkpoint_path
        self.s3_client = boto3.client('s3')
        self.bucket = checkpoint_path.replace('s3://', '').split('/')[0]
        self.key = '/'.join(checkpoint_path.replace('s3://', '').split('/')[1:] + ['last_processed_time.json'])

    def read_checkpoint(self):
        """读取上次处理的时间点"""
        try:
            response = self.s3_client.get_object(Bucket=self.bucket, Key=self.key)
            checkpoint_data = json.loads(response['Body'].read().decode('utf-8'))
            return datetime.strptime(checkpoint_data['last_processed_time'], '%Y-%m-%d %H:%M:%S')
        except Exception as e:
            print(f"No checkpoint found or error reading checkpoint: {str(e)}")
            return None

    def write_checkpoint(self, process_time):
        """写入处理时间点"""
        checkpoint_data = {
            'last_processed_time': process_time.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        try:
            self.s3_client.put_object(
                Bucket=self.bucket,
                Key=self.key,
                Body=json.dumps(checkpoint_data, indent=2)
            )
            print(f"Checkpoint updated: {process_time}")
        except Exception as e:
            print(f"Error writing checkpoint: {str(e)}")

class SmallFileMerger:
    def __init__(self, glue_context, args):
        """初始化合并器"""
        self.glue_context = glue_context
        self.spark = glue_context.spark_session
        self.s3_input_path = args['s3_input_path'].rstrip('/')
        self.target_size_mb = int(args['target_size_mb'])
        # 新增：文件大小阈值比例参数，默认为0.8（80%）
        self.size_threshold_ratio = float(args.get('size_threshold_ratio', 0.8))
        self.threshold_size_mb = self.target_size_mb * self.size_threshold_ratio
        self.checkpoint_path = args['checkpoint_path'].rstrip('/')
        self.checkpoint_mgr = CheckpointManager(self.checkpoint_path)
        self.s3_client = boto3.client('s3')
        self.args = args  # 保存args引用

    def get_time_range(self):
        """确定处理的时间范围"""
        end_time = datetime.now()
        
        if 'start_time' in self.args:  # 使用self.args替代args
            start_time = datetime.strptime(self.args['start_time'], '%Y-%m-%d %H:%M:%S')
            print(f"Using specified start time: {start_time}")
        else:
            start_time = self.checkpoint_mgr.read_checkpoint()
            if not start_time:
                # 使用一个非常早的时间，确保处理所有历史数据
                start_time = datetime(1970, 1, 1)
                print(f"No checkpoint found, processing all historical files from {start_time}")
            else:
                print(f"Resuming from checkpoint: {start_time}")
        
        return start_time, end_time

    def get_modified_files(self, start_time, end_time):
        """获取指定时间范围内修改的文件列表"""
        bucket_name = self.s3_input_path.replace('s3://', '').split('/')[0]
        prefix = '/'.join(self.s3_input_path.replace('s3://', '').split('/')[1:])
        
        modified_files = []
        medium_files = []  # 新增：接近目标大小的文件
        large_files = []
        paginator = self.s3_client.get_paginator('list_objects_v2')
        
        print(f"Scanning files modified between {start_time} and {end_time}")
        print(f"Target file size: {self.target_size_mb}MB")
        print(f"Size threshold ({self.size_threshold_ratio * 100}%): {self.threshold_size_mb}MB")
        
        # 获取文件列表
        file_list = []
        for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    if obj['Key'].endswith('.parquet'):
                        file_size_mb = obj['Size'] / (1024 * 1024)
                        file_info = {
                            'key': obj['Key'],
                            'last_modified': obj['LastModified'].replace(tzinfo=None),
                            'size': obj['Size'],
                            'size_mb': file_size_mb
                        }
                        file_list.append(file_info)

        # 对文件按修改时间排序
        file_list.sort(key=lambda x: x['last_modified'])
        
        # 找到时间范围内的最后一个文件
        last_valid_index = -1
        for i, file_info in enumerate(file_list):
            if file_info['last_modified'] <= end_time:
                last_valid_index = i
            else:
                break
        
        valid_files = file_list[:last_valid_index + 1]
        
        # 统计变量初始化
        small_files_count = 0
        small_files_total_size = 0
        medium_files_count = 0
        medium_files_total_size = 0
        large_files_count = 0
        large_files_total_size = 0
        
        # 按大小分类文件
        for file_info in valid_files:
            if start_time <= file_info['last_modified'] <= end_time:
                if file_info['size_mb'] >= self.target_size_mb:
                    large_files.append(file_info)
                    large_files_count += 1
                    large_files_total_size += file_info['size']
                elif file_info['size_mb'] >= self.threshold_size_mb:
                    medium_files.append(file_info)
                    medium_files_count += 1
                    medium_files_total_size += file_info['size']
                else:
                    modified_files.append(file_info['key'])
                    small_files_count += 1
                    small_files_total_size += file_info['size']
        
        # 打印详细的统计信息
        print("\nFile Analysis Report:")
        print(f"Small files (< {self.threshold_size_mb}MB):")
        print(f"  Count: {small_files_count}")
        print(f"  Total size: {small_files_total_size / (1024 * 1024):.2f}MB")
        print(f"Medium files ({self.threshold_size_mb}MB - {self.target_size_mb}MB):")
        print(f"  Count: {medium_files_count}")
        print(f"  Total size: {medium_files_total_size / (1024 * 1024):.2f}MB")
        print(f"Large files (>= {self.target_size_mb}MB):")
        print(f"  Count: {large_files_count}")
        print(f"  Total size: {large_files_total_size / (1024 * 1024):.2f}MB")
        print(f"Skipping {len(file_list) - len(valid_files)} newer files")
        
        # 打印被跳过的中等大小和大文件的信息
        if medium_files or large_files:
            print("\nFiles that will be skipped:")
            print("Medium files:")
            for file_info in medium_files[:3]:
                print(f"  - {file_info['key']} ({file_info['size_mb']:.2f}MB)")
            if len(medium_files) > 3:
                print(f"  ... and {len(medium_files) - 3} more medium files")
                
            print("Large files:")
            for file_info in large_files[:3]:
                print(f"  - {file_info['key']} ({file_info['size_mb']:.2f}MB)")
            if len(large_files) > 3:
                print(f"  ... and {len(large_files) - 3} more large files")
        
        return modified_files

    def merge_files(self, modified_files):
        """合并小文件"""
        if not modified_files:
            print("No files to process")
            return False

        # 只读取指定的文件列表，而不是整个目录
        print("Reading parquet files...")
        input_paths = [f"{self.s3_input_path}/{f}" for f in modified_files]
        df = self.spark.read.parquet(*input_paths)
        
        # 计算合适的分区数
        total_size_bytes = df.rdd.map(lambda x: len(str(x))).sum()
        total_size_mb = total_size_bytes / (1024 * 1024)
        num_partitions = max(1, int(total_size_mb / self.target_size_mb))
        
        print(f"Total size: {total_size_mb:.2f}MB, Creating {num_partitions} partitions")
        
        # 重新分区
        df = df.repartition(num_partitions)
        
        # 创建临时位置
        temp_location = f"{self.s3_input_path}_temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 写入临时位置
        print(f"Writing merged files to temporary location: {temp_location}")
        df.write.mode("overwrite").parquet(temp_location)

        # 移动文件
        self._move_files(temp_location, modified_files)
        return True

    def _move_files(self, temp_location, modified_files):
        """移动合并后的文件到目标位置"""
        source_bucket = temp_location.replace('s3://', '').split('/')[0]
        source_prefix = '/'.join(temp_location.replace('s3://', '').split('/')[1:])
        target_bucket = self.s3_input_path.replace('s3://', '').split('/')[0]
        target_prefix = '/'.join(self.s3_input_path.replace('s3://', '').split('/')[1:])

        # 删除原始文件
        print("Deleting original files...")
        for key in modified_files:
            try:
                self.s3_client.delete_object(Bucket=target_bucket, Key=key)
            except Exception as e:
                print(f"Error deleting file {key}: {str(e)}")

        # 移动新文件
        print("Moving merged files to target location...")
        paginator = self.s3_client.get_paginator('list_objects_v2')
        for page in paginator.paginate(Bucket=source_bucket, Prefix=source_prefix):
            if 'Contents' in page:
                for obj in page['Contents']:
                    if obj['Key'].endswith('.parquet'):
                        new_key = f"{target_prefix}/{obj['Key'].split('/')[-1]}"
                        try:
                            self.s3_client.copy_object(
                                CopySource={'Bucket': source_bucket, 'Key': obj['Key']},
                                Bucket=target_bucket,
                                Key=new_key
                            )
                            self.s3_client.delete_object(Bucket=source_bucket, Key=obj['Key'])
                        except Exception as e:
                            print(f"Error processing file {obj['Key']}: {str(e)}")

        # 清理临时目录
        try:
            self.s3_client.delete_object(Bucket=source_bucket, Key=source_prefix)
        except Exception as e:
            print(f"Error cleaning up temp directory: {str(e)}")

    def get_subdirectories(self):
        """获取所有子目录"""
        bucket_name = self.s3_input_path.replace('s3://', '').split('/')[0]
        prefix = '/'.join(self.s3_input_path.replace('s3://', '').split('/')[1:])
        if not prefix.endswith('/'):
            prefix += '/'

        directories = set()
        paginator = self.s3_client.get_paginator('list_objects_v2')
        
        print(f"Scanning for subdirectories in {self.s3_input_path}")
        
        for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/'):
            if 'CommonPrefixes' in page:
                for prefix_obj in page['CommonPrefixes']:
                    dir_path = f"s3://{bucket_name}/{prefix_obj['Prefix']}"
                    directories.add(dir_path)
        
        print(f"Found {len(directories)} subdirectories")
        return directories

    def process_directory(self, directory_path):
        """处理单个目录中的文件"""
        print(f"\nProcessing directory: {directory_path}")
        
        # 保存原始的s3_input_path
        original_path = self.s3_input_path
        
        try:
            # 临时将s3_input_path设置为当前处理的目录
            self.s3_input_path = directory_path
            
            # 获取处理时间范围
            start_time, end_time = self.get_time_range()
            
            # 获取需要处理的文件
            modified_files = self.get_modified_files(start_time, end_time)
            
            # 合并文件
            if modified_files:
                if self.merge_files(modified_files):
                    # 更新checkpoint
                    self.checkpoint_mgr.write_checkpoint(end_time)
                    print(f"File merge completed successfully for {directory_path}")
                    return True
            else:
                print(f"No files to process in {directory_path}")
            
            return False
            
        finally:
            # 恢复原始的s3_input_path
            self.s3_input_path = original_path

def main():
    # 初始化 Glue 上下文
    required_args = [
        'JOB_NAME',
        's3_input_path',
        'target_size_mb',
        'checkpoint_path'
    ]
    
    # 获取必选参数
    args = getResolvedOptions(sys.argv, required_args)
    
    # 尝试获取可选参数
    try:
        optional_args = getResolvedOptions(sys.argv, ['start_time', 'size_threshold_ratio'])
        args.update(optional_args)
    except:
        pass

    sc = SparkContext()
    glue_context = GlueContext(sc)
    spark = glue_context.spark_session
    job = Job(glue_context)
    job.init(args['JOB_NAME'], args)

    # 创建合并器实例
    merger = SmallFileMerger(glue_context, args)
    
    try:
        # 获取所有子目录
        subdirectories = merger.get_subdirectories()
        
        if not subdirectories:
            print("No subdirectories found to process")
            job.commit()
            return
            
        # 处理每个子目录
        processed_dirs = 0
        for directory in subdirectories:
            try:
                if merger.process_directory(directory):
                    processed_dirs += 1
            except Exception as e:
                print(f"Error processing directory {directory}: {str(e)}")
                # 继续处理下一个目录
                continue
        
        print(f"\nProcessing complete. Successfully processed {processed_dirs} out of {len(subdirectories)} directories")
        job.commit()
        
    except Exception as e:
        print(f"Error during file merge: {str(e)}")
        raise e

if __name__ == "__main__":
    main()











